# Security Guide - Hướng dẫn Bảo mật

## 1. Code Signing (<PERSON><PERSON> số)

### Tự tạo certificate (Testing):
```powershell
# Chạy PowerShell as Administrator
powershell -ExecutionPolicy Bypass -File setup_files\create_cert.ps1
```

### Mua certificate chính thức:
- **DigiCert**: https://www.digicert.com/code-signing/
- **Sectigo**: https://sectigo.com/ssl-certificates-tls/code-signing
- **GlobalSign**: https://www.globalsign.com/en/code-signing-certificate

## 2. Ký file executable:
```cmd
signtool sign /f setup_files\codesign.pfx /p password123 /t http://timestamp.digicert.com setup_files\TradingView.exe
```

## 3. Kiểm tra chữ ký:
```cmd
signtool verify /pa setup_files\TradingView.exe
```

## 4. Tăng độ tin cậy:

### Legitimate practices:
- ✅ Sử dụng code signing certificate
- ✅ Build reputation qua thời gian
- ✅ Tạo installer chuyên nghiệp
- ✅ Thêm metadata đầy đủ
- ✅ Sử dụng HTTPS cho download

### Tránh:
- ❌ Fake certificates
- ❌ Malicious behavior
- ❌ Obfuscation không cần thiết
- ❌ Bypass security bằng cách bất hợp pháp

## 5. Windows SmartScreen:

SmartScreen sẽ tin tậy ứng dụng hơn khi:
- Có code signing certificate hợp lệ
- Nhiều người dùng download và sử dụng
- Không có báo cáo malware
- Metadata đầy đủ và chính xác

## 6. Best Practices:

1. **Always sign your code** với certificate hợp lệ
2. **Use HTTPS** cho tất cả downloads
3. **Provide clear documentation** về ứng dụng
4. **Be transparent** về chức năng
5. **Follow Microsoft guidelines** cho Windows apps
