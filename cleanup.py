#!/usr/bin/env python3
# -*- coding: utf-8 -*-
"""
Cleanup Script - Dọn dẹp các file không cần thiết
"""

import os
import shutil
import glob
from pathlib import Path

class ProjectCleaner:
    def __init__(self):
        self.root_dir = Path(".")
        self.cleaned_files = []
        self.cleaned_dirs = []
        
    def clean_python_cache(self):
        """Dọn dẹp Python cache files"""
        print("🧹 Cleaning Python cache files...")
        
        # __pycache__ directories
        pycache_dirs = list(self.root_dir.rglob("__pycache__"))
        for dir_path in pycache_dirs:
            if dir_path.is_dir():
                shutil.rmtree(dir_path)
                self.cleaned_dirs.append(str(dir_path))
                print(f"   ✅ Removed: {dir_path}")
        
        # .pyc files
        pyc_files = list(self.root_dir.rglob("*.pyc"))
        for file_path in pyc_files:
            file_path.unlink()
            self.cleaned_files.append(str(file_path))
            print(f"   ✅ Removed: {file_path}")
        
        # .pyo files
        pyo_files = list(self.root_dir.rglob("*.pyo"))
        for file_path in pyo_files:
            file_path.unlink()
            self.cleaned_files.append(str(file_path))
            print(f"   ✅ Removed: {file_path}")

    def clean_build_artifacts(self):
        """Dọn dẹp build artifacts"""
        print("🧹 Cleaning build artifacts...")
        
        # build directory
        build_dir = self.root_dir / "build"
        if build_dir.exists():
            shutil.rmtree(build_dir)
            self.cleaned_dirs.append(str(build_dir))
            print(f"   ✅ Removed: {build_dir}")
        
        # .egg-info directories
        egg_dirs = list(self.root_dir.rglob("*.egg-info"))
        for dir_path in egg_dirs:
            if dir_path.is_dir():
                shutil.rmtree(dir_path)
                self.cleaned_dirs.append(str(dir_path))
                print(f"   ✅ Removed: {dir_path}")

    def clean_temp_files(self):
        """Dọn dẹp temporary files"""
        print("🧹 Cleaning temporary files...")
        
        temp_patterns = [
            "*.tmp", "*.temp", "*.log", "*.bak", 
            "*~", ".DS_Store", "Thumbs.db"
        ]
        
        for pattern in temp_patterns:
            temp_files = list(self.root_dir.rglob(pattern))
            for file_path in temp_files:
                if file_path.is_file():
                    file_path.unlink()
                    self.cleaned_files.append(str(file_path))
                    print(f"   ✅ Removed: {file_path}")

    def clean_dist_duplicates(self):
        """Dọn dẹp file trùng lặp trong dist"""
        print("🧹 Cleaning dist duplicates...")
        
        dist_dir = self.root_dir / "dist"
        setup_dir = self.root_dir / "setup_files"
        
        if dist_dir.exists() and setup_dir.exists():
            # Remove duplicate TradingView.exe in setup_files if exists in dist
            setup_exe = setup_dir / "TradingView.exe"
            dist_exe = dist_dir / "TradingView.exe"
            
            if setup_exe.exists() and dist_exe.exists():
                setup_exe.unlink()
                self.cleaned_files.append(str(setup_exe))
                print(f"   ✅ Removed duplicate: {setup_exe}")

    def clean_old_installers(self):
        """Dọn dẹp các installer cũ"""
        print("🧹 Cleaning old installers...")
        
        setup_dir = self.root_dir / "setup_files"
        if setup_dir.exists():
            # Remove old setup files
            old_setups = list(setup_dir.glob("*Setup*.exe"))
            for setup_file in old_setups:
                setup_file.unlink()
                self.cleaned_files.append(str(setup_file))
                print(f"   ✅ Removed old installer: {setup_file}")

    def optimize_venv(self):
        """Tối ưu virtual environment"""
        print("🧹 Optimizing virtual environment...")
        
        venv_dir = self.root_dir / "venv"
        if venv_dir.exists():
            # Clean pip cache in venv
            pip_cache = venv_dir / "Lib" / "site-packages" / "pip" / "_internal" / "cache"
            if pip_cache.exists():
                shutil.rmtree(pip_cache)
                self.cleaned_dirs.append(str(pip_cache))
                print(f"   ✅ Removed pip cache: {pip_cache}")

    def show_summary(self):
        """Hiển thị tổng kết"""
        print("\n📊 Cleanup Summary:")
        print("=" * 40)
        print(f"🗂️  Directories removed: {len(self.cleaned_dirs)}")
        print(f"📄 Files removed: {len(self.cleaned_files)}")
        
        if self.cleaned_dirs:
            print("\n📁 Removed directories:")
            for dir_path in self.cleaned_dirs:
                print(f"   - {dir_path}")
        
        if self.cleaned_files:
            print(f"\n📄 Removed files: ({len(self.cleaned_files)} total)")
            # Show only first 10 files to avoid spam
            for file_path in self.cleaned_files[:10]:
                print(f"   - {file_path}")
            if len(self.cleaned_files) > 10:
                print(f"   ... and {len(self.cleaned_files) - 10} more files")

def main():
    print("🧹 Project Cleanup Tool")
    print("Dọn dẹp các file không cần thiết")
    print("=" * 40)
    
    cleaner = ProjectCleaner()
    
    # Perform cleanup
    cleaner.clean_python_cache()
    cleaner.clean_build_artifacts()
    cleaner.clean_temp_files()
    cleaner.clean_dist_duplicates()
    cleaner.clean_old_installers()
    cleaner.optimize_venv()
    
    # Show summary
    cleaner.show_summary()
    
    print("\n✅ Cleanup completed!")
    print("💡 Your project is now cleaner and optimized")

if __name__ == "__main__":
    try:
        main()
    except KeyboardInterrupt:
        print("\n⚠️ Cleanup interrupted")
    except Exception as e:
        print(f"\n❌ Error during cleanup: {e}")
