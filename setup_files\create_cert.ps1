
# PowerShell script to create self-signed certificate
$cert = New-SelfSignedCertificate -Type CodeSigningCert -Subject "CN=TradingView Desktop" -KeyUsage DigitalSignature -FriendlyName "TradingView Code Signing" -CertStoreLocation "Cert:\CurrentUser\My" -TextExtension @("*********={text}*******.*******.3", "*********={text}")

# Export certificate
$password = ConvertTo-SecureString -String "password123" -Force -AsPlainText
Export-PfxCertificate -Cert $cert -FilePath "setup_files\codesign.pfx" -Password $password

Write-Host "Certificate created: setup_files\codesign.pfx"
Write-Host "Password: password123"
