#!/usr/bin/env python3
# -*- coding: utf-8 -*-
"""
Deep Cleanup - Xóa tất cả file build và không cần thiết
"""

import os
import shutil
import glob
from pathlib import Path

class DeepCleaner:
    def __init__(self):
        self.root_dir = Path(".")
        self.removed_files = []
        self.removed_dirs = []
        
    def clean_build_outputs(self):
        """Xóa tất cả build outputs"""
        print("🗑️ Cleaning build outputs...")
        
        # X<PERSON>a thư mục dist
        dist_dir = self.root_dir / "dist"
        if dist_dir.exists():
            shutil.rmtree(dist_dir)
            self.removed_dirs.append(str(dist_dir))
            print(f"   ✅ Removed: {dist_dir}")
        
        # Xóa thư mục build
        build_dir = self.root_dir / "build"
        if build_dir.exists():
            shutil.rmtree(build_dir)
            self.removed_dirs.append(str(build_dir))
            print(f"   ✅ Removed: {build_dir}")
        
        # Xóa các file .spec
        spec_files = list(self.root_dir.glob("*.spec"))
        for spec_file in spec_files:
            spec_file.unlink()
            self.removed_files.append(str(spec_file))
            print(f"   ✅ Removed: {spec_file}")

    def clean_setup_files(self):
        """Xóa các file setup cũ"""
        print("🗑️ Cleaning old setup files...")
        
        setup_dir = self.root_dir / "setup_files"
        if setup_dir.exists():
            # Xóa các file executable cũ
            exe_files = list(setup_dir.glob("*.exe"))
            for exe_file in exe_files:
                exe_file.unlink()
                self.removed_files.append(str(exe_file))
                print(f"   ✅ Removed: {exe_file}")
            
            # Xóa các file installer cũ
            installer_files = list(setup_dir.glob("*Setup*.exe"))
            for installer_file in installer_files:
                installer_file.unlink()
                self.removed_files.append(str(installer_file))
                print(f"   ✅ Removed: {installer_file}")

    def clean_temp_and_cache(self):
        """Xóa temp files và cache"""
        print("🗑️ Cleaning temp files and cache...")
        
        # Xóa __pycache__ directories
        pycache_dirs = list(self.root_dir.rglob("__pycache__"))
        for dir_path in pycache_dirs:
            if dir_path.is_dir():
                shutil.rmtree(dir_path)
                self.removed_dirs.append(str(dir_path))
                print(f"   ✅ Removed: {dir_path}")
        
        # Xóa .pyc files
        pyc_files = list(self.root_dir.rglob("*.pyc"))
        for file_path in pyc_files:
            file_path.unlink()
            self.removed_files.append(str(file_path))
            print(f"   ✅ Removed: {file_path}")
        
        # Xóa temp files
        temp_patterns = ["*.tmp", "*.temp", "*.log", "*.bak", "*~"]
        for pattern in temp_patterns:
            temp_files = list(self.root_dir.rglob(pattern))
            for file_path in temp_files:
                if file_path.is_file():
                    file_path.unlink()
                    self.removed_files.append(str(file_path))
                    print(f"   ✅ Removed: {file_path}")

    def clean_unnecessary_files(self):
        """Xóa các file không cần thiết khác"""
        print("🗑️ Cleaning unnecessary files...")
        
        # Xóa các file zip cũ
        zip_files = list(self.root_dir.glob("*.zip"))
        for zip_file in zip_files:
            zip_file.unlink()
            self.removed_files.append(str(zip_file))
            print(f"   ✅ Removed: {zip_file}")
        
        # Xóa các file certificate test cũ
        cert_files = list(self.root_dir.rglob("*.pfx"))
        for cert_file in cert_files:
            cert_file.unlink()
            self.removed_files.append(str(cert_file))
            print(f"   ✅ Removed: {cert_file}")

    def preserve_important_files(self):
        """Liệt kê các file quan trọng được giữ lại"""
        print("📋 Important files preserved:")
        important_files = [
            "main.py", "social.py", "wallet.py", 
            "build.py", "build_signed.py", "create_installer.py",
            "requirements.txt", "logo.ico", "README.md"
        ]
        
        for file_name in important_files:
            if os.path.exists(file_name):
                print(f"   ✅ Kept: {file_name}")

    def show_summary(self):
        """Hiển thị tổng kết"""
        print("\n📊 Deep Cleanup Summary:")
        print("=" * 50)
        print(f"🗂️  Directories removed: {len(self.removed_dirs)}")
        print(f"📄 Files removed: {len(self.removed_files)}")
        
        total_space_saved = 0
        print(f"\n💾 Estimated space saved: Calculating...")
        
        print("\n🎯 Ready for fresh build!")

def main():
    print("🗑️ Deep Cleanup Tool")
    print("Xóa tất cả file build và không cần thiết")
    print("=" * 50)
    
    cleaner = DeepCleaner()
    
    # Confirm with user
    print("⚠️  This will remove ALL build outputs and temporary files!")
    print("📋 Files that will be kept:")
    print("   - Source code (*.py)")
    print("   - Requirements (requirements.txt)")
    print("   - Assets (logo.ico)")
    print("   - Documentation (README.md)")
    print("   - Virtual environment (venv/)")
    
    response = input("\n❓ Continue? (y/N): ").strip().lower()
    if response != 'y':
        print("❌ Cleanup cancelled")
        return
    
    # Perform deep cleanup
    cleaner.clean_build_outputs()
    cleaner.clean_setup_files()
    cleaner.clean_temp_and_cache()
    cleaner.clean_unnecessary_files()
    cleaner.preserve_important_files()
    cleaner.show_summary()
    
    print("\n✅ Deep cleanup completed!")
    print("🚀 Ready to build fresh executable")

if __name__ == "__main__":
    try:
        main()
    except KeyboardInterrupt:
        print("\n⚠️ Cleanup interrupted")
    except Exception as e:
        print(f"\n❌ Error during cleanup: {e}")
