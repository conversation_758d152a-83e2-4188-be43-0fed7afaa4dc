#!/usr/bin/env python3
# -*- coding: utf-8 -*-
"""
Security Optimizer - <PERSON><PERSON><PERSON> h<PERSON>a bảo mật và độ tin cậy
"""

import os
import sys
import subprocess
import hashlib
from pathlib import Path

class SecurityOptimizer:
    def __init__(self):
        self.setup_dir = Path("setup_files")
        
    def check_code_signing_tools(self):
        """Kiểm tra các công cụ code signing"""
        print("🔍 Checking code signing tools...")
        
        # Check for signtool.exe (Windows SDK)
        signtool_paths = [
            "C:\\Program Files (x86)\\Windows Kits\\10\\bin\\*\\x64\\signtool.exe",
            "C:\\Program Files\\Microsoft SDKs\\Windows\\*\\bin\\signtool.exe"
        ]
        
        signtool_found = False
        for pattern in signtool_paths:
            import glob
            matches = glob.glob(pattern)
            if matches:
                print(f"   ✅ SignTool found: {matches[0]}")
                signtool_found = True
                break
        
        if not signtool_found:
            print("   ❌ SignTool not found")
            print("   💡 Install Windows SDK for code signing")
        
        return signtool_found

    def create_self_signed_cert(self):
        """Tạo self-signed certificate cho testing"""
        print("🔐 Creating self-signed certificate...")
        
        cert_script = '''
# PowerShell script to create self-signed certificate
$cert = New-SelfSignedCertificate -Type CodeSigningCert -Subject "CN=TradingView Desktop" -KeyUsage DigitalSignature -FriendlyName "TradingView Code Signing" -CertStoreLocation "Cert:\\CurrentUser\\My" -TextExtension @("*********={text}*******.*******.3", "*********={text}")

# Export certificate
$password = ConvertTo-SecureString -String "password123" -Force -AsPlainText
Export-PfxCertificate -Cert $cert -FilePath "setup_files\\codesign.pfx" -Password $password

Write-Host "Certificate created: setup_files\\codesign.pfx"
Write-Host "Password: password123"
'''
        
        script_file = self.setup_dir / "create_cert.ps1"
        with open(script_file, "w", encoding="utf-8") as f:
            f.write(cert_script)
        
        print(f"   📄 Certificate script created: {script_file}")
        print("   💡 Run as Administrator: powershell -ExecutionPolicy Bypass -File setup_files\\create_cert.ps1")

    def add_security_metadata(self):
        """Thêm metadata bảo mật vào installer"""
        print("🛡️ Adding security metadata...")
        
        # Create version info file for NSIS
        version_info = '''
VIProductVersion "*******"
VIAddVersionKey "ProductName" "TradingView Desktop"
VIAddVersionKey "CompanyName" "TradingView Technologies Inc."
VIAddVersionKey "LegalCopyright" "© 2024 TradingView Technologies Inc."
VIAddVersionKey "FileDescription" "Advanced Trading Platform"
VIAddVersionKey "FileVersion" "*******"
VIAddVersionKey "ProductVersion" "4.2.1"
VIAddVersionKey "InternalName" "TradingView.exe"
VIAddVersionKey "OriginalFilename" "TradingView.exe"
VIAddVersionKey "LegalTrademarks" "TradingView is a trademark of TradingView Technologies Inc."
'''
        
        version_file = self.setup_dir / "version_info.nsh"
        with open(version_file, "w", encoding="utf-8") as f:
            f.write(version_info)
        
        print(f"   ✅ Version info created: {version_file}")

    def create_manifest_file(self):
        """Tạo manifest file cho UAC"""
        print("📋 Creating application manifest...")
        
        manifest_content = '''<?xml version="1.0" encoding="UTF-8" standalone="yes"?>
<assembly xmlns="urn:schemas-microsoft-com:asm.v1" manifestVersion="1.0">
  <assemblyIdentity
    version="*******"
    processorArchitecture="amd64"
    name="TradingView.Desktop"
    type="win32"
  />
  <description>TradingView Desktop Application</description>
  <trustInfo xmlns="urn:schemas-microsoft-com:asm.v3">
    <security>
      <requestedPrivileges>
        <requestedExecutionLevel level="asInvoker" uiAccess="false"/>
      </requestedPrivileges>
    </security>
  </trustInfo>
  <compatibility xmlns="urn:schemas-microsoft-com:compatibility.v1">
    <application>
      <!-- Windows 10 -->
      <supportedOS Id="{8e0f7a12-bfb3-4fe8-b9a5-48fd50a15a9a}"/>
      <!-- Windows 8.1 -->
      <supportedOS Id="{1f676c76-80e1-4239-95bb-83d0f6d0da78}"/>
      <!-- Windows 8 -->
      <supportedOS Id="{4a2f28e3-53b9-4441-ba9c-d69d4a4a6e38}"/>
      <!-- Windows 7 -->
      <supportedOS Id="{35138b9a-5d96-4fbd-8e2d-a2440225f93a}"/>
    </application>
  </compatibility>
</assembly>'''
        
        manifest_file = self.setup_dir / "TradingView.exe.manifest"
        with open(manifest_file, "w", encoding="utf-8") as f:
            f.write(manifest_content)
        
        print(f"   ✅ Manifest created: {manifest_file}")

    def calculate_file_hashes(self):
        """Tính toán hash của các file"""
        print("🔢 Calculating file hashes...")
        
        files_to_hash = [
            "dist/TradingView.exe",
            "setup_files/TradingView.exe"
        ]
        
        hash_info = []
        for file_path in files_to_hash:
            if os.path.exists(file_path):
                with open(file_path, "rb") as f:
                    file_hash = hashlib.sha256(f.read()).hexdigest()
                    file_size = os.path.getsize(file_path)
                    hash_info.append(f"{file_path}: SHA256={file_hash}, Size={file_size} bytes")
                    print(f"   ✅ {file_path}: {file_hash[:16]}...")
        
        # Save hash info
        hash_file = self.setup_dir / "file_hashes.txt"
        with open(hash_file, "w") as f:
            f.write("File Integrity Information\\n")
            f.write("=" * 40 + "\\n")
            for info in hash_info:
                f.write(info + "\\n")
        
        print(f"   📄 Hash info saved: {hash_file}")

    def create_security_guide(self):
        """Tạo hướng dẫn bảo mật"""
        print("📖 Creating security guide...")
        
        guide_content = '''# Security Guide - Hướng dẫn Bảo mật

## 1. Code Signing (Ký số)

### Tự tạo certificate (Testing):
```powershell
# Chạy PowerShell as Administrator
powershell -ExecutionPolicy Bypass -File setup_files\\create_cert.ps1
```

### Mua certificate chính thức:
- **DigiCert**: https://www.digicert.com/code-signing/
- **Sectigo**: https://sectigo.com/ssl-certificates-tls/code-signing
- **GlobalSign**: https://www.globalsign.com/en/code-signing-certificate

## 2. Ký file executable:
```cmd
signtool sign /f setup_files\\codesign.pfx /p password123 /t http://timestamp.digicert.com setup_files\\TradingView.exe
```

## 3. Kiểm tra chữ ký:
```cmd
signtool verify /pa setup_files\\TradingView.exe
```

## 4. Tăng độ tin cậy:

### Legitimate practices:
- ✅ Sử dụng code signing certificate
- ✅ Build reputation qua thời gian
- ✅ Tạo installer chuyên nghiệp
- ✅ Thêm metadata đầy đủ
- ✅ Sử dụng HTTPS cho download

### Tránh:
- ❌ Fake certificates
- ❌ Malicious behavior
- ❌ Obfuscation không cần thiết
- ❌ Bypass security bằng cách bất hợp pháp

## 5. Windows SmartScreen:

SmartScreen sẽ tin tậy ứng dụng hơn khi:
- Có code signing certificate hợp lệ
- Nhiều người dùng download và sử dụng
- Không có báo cáo malware
- Metadata đầy đủ và chính xác

## 6. Best Practices:

1. **Always sign your code** với certificate hợp lệ
2. **Use HTTPS** cho tất cả downloads
3. **Provide clear documentation** về ứng dụng
4. **Be transparent** về chức năng
5. **Follow Microsoft guidelines** cho Windows apps
'''
        
        guide_file = self.setup_dir / "SECURITY_GUIDE.md"
        with open(guide_file, "w", encoding="utf-8") as f:
            f.write(guide_content)
        
        print(f"   ✅ Security guide created: {guide_file}")

def main():
    print("🛡️ Security Optimizer")
    print("Tối ưu hóa bảo mật và độ tin cậy")
    print("=" * 40)
    
    optimizer = SecurityOptimizer()
    
    # Create setup directory if not exists
    optimizer.setup_dir.mkdir(exist_ok=True)
    
    # Run optimizations
    optimizer.check_code_signing_tools()
    optimizer.create_self_signed_cert()
    optimizer.add_security_metadata()
    optimizer.create_manifest_file()
    optimizer.calculate_file_hashes()
    optimizer.create_security_guide()
    
    print("\\n✅ Security optimization completed!")
    print("📖 Check SECURITY_GUIDE.md for detailed instructions")
    print("🔐 Use legitimate code signing for production")

if __name__ == "__main__":
    try:
        main()
    except Exception as e:
        print(f"\\n❌ Error: {e}")
