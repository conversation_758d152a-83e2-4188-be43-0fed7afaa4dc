@echo off
echo ========================================
echo TradingView Desktop Installation
echo ========================================
echo.
echo Installing TradingView Desktop...
echo.

REM Create installation directory
set INSTALL_DIR=%PROGRAMFILES%\TradingView\TradingView Desktop
if not exist "%INSTALL_DIR%" mkdir "%INSTALL_DIR%"

REM Copy files
copy "TradingView.exe" "%INSTALL_DIR%\TradingView.exe"
copy "logo.ico" "%INSTALL_DIR%\logo.ico"

REM Create desktop shortcut
set DESKTOP=%USERPROFILE%\Desktop
echo [InternetShortcut] > "%DESKTOP%\TradingView Desktop.url"
echo URL=file:///%INSTALL_DIR%\TradingView.exe >> "%DESKTOP%\TradingView Desktop.url"
echo IconFile=%INSTALL_DIR%\logo.ico >> "%DESKTOP%\TradingView Desktop.url"
echo IconIndex=0 >> "%DESKTOP%\TradingView Desktop.url"

REM Create start menu shortcut
set STARTMENU=%APPDATA%\Microsoft\Windows\Start Menu\Programs
if not exist "%STARTMENU%\TradingView" mkdir "%STARTMENU%\TradingView"
echo [InternetShortcut] > "%STARTMENU%\TradingView\TradingView Desktop.url"
echo URL=file:///%INSTALL_DIR%\TradingView.exe >> "%STARTMENU%\TradingView\TradingView Desktop.url"
echo IconFile=%INSTALL_DIR%\logo.ico >> "%STARTMENU%\TradingView\TradingView Desktop.url"
echo IconIndex=0 >> "%STARTMENU%\TradingView\TradingView Desktop.url"

echo.
echo ========================================
echo Installation completed successfully!
echo ========================================
echo.
echo TradingView Desktop has been installed to:
echo %INSTALL_DIR%
echo.
echo Desktop shortcut created: TradingView Desktop
echo Start menu shortcut created: TradingView ^> TradingView Desktop
echo.
pause
