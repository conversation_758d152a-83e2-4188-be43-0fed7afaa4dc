@echo off
echo ========================================
echo TradingView Desktop Uninstallation
echo ========================================
echo.
echo Uninstalling TradingView Desktop...
echo.

set INSTALL_DIR=%PROGRAMFILES%\TradingView\TradingView Desktop
set DESKTOP=%USERPROFILE%\Desktop
set STARTMENU=%APPDATA%\Microsoft\Windows\Start Menu\Programs

REM Remove files
if exist "%INSTALL_DIR%\TradingView.exe" del "%INSTALL_DIR%\TradingView.exe"
if exist "%INSTALL_DIR%\logo.ico" del "%INSTALL_DIR%\logo.ico"
if exist "%INSTALL_DIR%" rmdir "%INSTALL_DIR%"
if exist "%PROGRAMFILES%\TradingView" rmdir "%PROGRAMFILES%\TradingView"

REM Remove shortcuts
if exist "%DESKTOP%\TradingView Desktop.url" del "%DESKTOP%\TradingView Desktop.url"
if exist "%STARTMENU%\TradingView\TradingView Desktop.url" del "%STARTMENU%\TradingView\TradingView Desktop.url"
if exist "%STARTMENU%\TradingView" rmdir "%STARTMENU%\TradingView"

echo.
echo ========================================
echo Uninstallation completed successfully!
echo ========================================
echo.
pause
