#!/usr/bin/env python3
# -*- coding: utf-8 -*-
"""
Simple Installer Creator - Tạo installer đơn gi<PERSON>n không cần NSIS/Inno Setup
"""

import os
import shutil
import zipfile
from pathlib import Path

class SimpleInstaller:
    def __init__(self):
        self.setup_dir = Path("setup_files")
        self.output_dir = Path("installer_output")
        self.output_dir.mkdir(exist_ok=True)
        
    def create_portable_package(self):
        """Tạo portable package"""
        print("📦 Creating portable package...")
        
        portable_dir = self.output_dir / "TradingView_Portable"
        portable_dir.mkdir(exist_ok=True)
        
        # Copy executable
        exe_source = Path("dist/TradingView.exe")
        if exe_source.exists():
            shutil.copy2(exe_source, portable_dir / "TradingView.exe")
            print(f"   ✅ Copied: TradingView.exe")
        
        # Copy logo
        if os.path.exists("logo.ico"):
            shutil.copy2("logo.ico", portable_dir / "logo.ico")
            print(f"   ✅ Copied: logo.ico")
        
        # Create README
        readme_content = """# TradingView Desktop - Portable Version

## Hướng dẫn sử dụng:

1. **Chạy ứng dụng**: Double-click vào `TradingView.exe`
2. **Portable**: Không cần cài đặt, chạy trực tiếp
3. **An toàn**: Đã được tối ưu hóa bảo mật

## Tính năng:
- ✅ Advanced Trading Platform
- ✅ Real-time Market Data
- ✅ Professional Charts
- ✅ Portfolio Management
- ✅ Social Trading Features

## Hỗ trợ:
- Website: https://www.tradingview.com
- Support: https://www.tradingview.com/support

## Phiên bản: 4.2.1
## Build: Professional Edition
"""
        
        readme_file = portable_dir / "README.txt"
        with open(readme_file, "w", encoding="utf-8") as f:
            f.write(readme_content)
        print(f"   ✅ Created: README.txt")
        
        return portable_dir

    def create_zip_installer(self):
        """Tạo ZIP installer"""
        print("📦 Creating ZIP installer...")
        
        zip_file = self.output_dir / "TradingView_Desktop_v4.2.1.zip"
        
        with zipfile.ZipFile(zip_file, 'w', zipfile.ZIP_DEFLATED) as zf:
            # Add executable
            exe_source = Path("dist/TradingView.exe")
            if exe_source.exists():
                zf.write(exe_source, "TradingView.exe")
                print(f"   ✅ Added: TradingView.exe")
            
            # Add logo
            if os.path.exists("logo.ico"):
                zf.write("logo.ico", "logo.ico")
                print(f"   ✅ Added: logo.ico")
            
            # Add installation script
            install_script = """@echo off
echo ========================================
echo TradingView Desktop Installation
echo ========================================
echo.
echo Installing TradingView Desktop...
echo.

REM Create installation directory
set INSTALL_DIR=%PROGRAMFILES%\\TradingView\\TradingView Desktop
if not exist "%INSTALL_DIR%" mkdir "%INSTALL_DIR%"

REM Copy files
copy "TradingView.exe" "%INSTALL_DIR%\\TradingView.exe"
copy "logo.ico" "%INSTALL_DIR%\\logo.ico"

REM Create desktop shortcut
set DESKTOP=%USERPROFILE%\\Desktop
echo [InternetShortcut] > "%DESKTOP%\\TradingView Desktop.url"
echo URL=file:///%INSTALL_DIR%\\TradingView.exe >> "%DESKTOP%\\TradingView Desktop.url"
echo IconFile=%INSTALL_DIR%\\logo.ico >> "%DESKTOP%\\TradingView Desktop.url"
echo IconIndex=0 >> "%DESKTOP%\\TradingView Desktop.url"

REM Create start menu shortcut
set STARTMENU=%APPDATA%\\Microsoft\\Windows\\Start Menu\\Programs
if not exist "%STARTMENU%\\TradingView" mkdir "%STARTMENU%\\TradingView"
echo [InternetShortcut] > "%STARTMENU%\\TradingView\\TradingView Desktop.url"
echo URL=file:///%INSTALL_DIR%\\TradingView.exe >> "%STARTMENU%\\TradingView\\TradingView Desktop.url"
echo IconFile=%INSTALL_DIR%\\logo.ico >> "%STARTMENU%\\TradingView\\TradingView Desktop.url"
echo IconIndex=0 >> "%STARTMENU%\\TradingView\\TradingView Desktop.url"

echo.
echo ========================================
echo Installation completed successfully!
echo ========================================
echo.
echo TradingView Desktop has been installed to:
echo %INSTALL_DIR%
echo.
echo Desktop shortcut created: TradingView Desktop
echo Start menu shortcut created: TradingView ^> TradingView Desktop
echo.
pause
"""
            
            zf.writestr("install.bat", install_script)
            print(f"   ✅ Added: install.bat")
            
            # Add uninstall script
            uninstall_script = """@echo off
echo ========================================
echo TradingView Desktop Uninstallation
echo ========================================
echo.
echo Uninstalling TradingView Desktop...
echo.

set INSTALL_DIR=%PROGRAMFILES%\\TradingView\\TradingView Desktop
set DESKTOP=%USERPROFILE%\\Desktop
set STARTMENU=%APPDATA%\\Microsoft\\Windows\\Start Menu\\Programs

REM Remove files
if exist "%INSTALL_DIR%\\TradingView.exe" del "%INSTALL_DIR%\\TradingView.exe"
if exist "%INSTALL_DIR%\\logo.ico" del "%INSTALL_DIR%\\logo.ico"
if exist "%INSTALL_DIR%" rmdir "%INSTALL_DIR%"
if exist "%PROGRAMFILES%\\TradingView" rmdir "%PROGRAMFILES%\\TradingView"

REM Remove shortcuts
if exist "%DESKTOP%\\TradingView Desktop.url" del "%DESKTOP%\\TradingView Desktop.url"
if exist "%STARTMENU%\\TradingView\\TradingView Desktop.url" del "%STARTMENU%\\TradingView\\TradingView Desktop.url"
if exist "%STARTMENU%\\TradingView" rmdir "%STARTMENU%\\TradingView"

echo.
echo ========================================
echo Uninstallation completed successfully!
echo ========================================
echo.
pause
"""
            
            zf.writestr("uninstall.bat", uninstall_script)
            print(f"   ✅ Added: uninstall.bat")
            
            # Add README
            readme_content = """TradingView Desktop v4.2.1 - Professional Edition

INSTALLATION INSTRUCTIONS:
==========================

Option 1: Portable (Recommended)
- Simply run TradingView.exe directly
- No installation required
- Can run from any location

Option 2: Full Installation
- Run install.bat as Administrator
- Creates desktop and start menu shortcuts
- Installs to Program Files

UNINSTALLATION:
===============
- Run uninstall.bat as Administrator
- Removes all files and shortcuts

FEATURES:
=========
✅ Advanced Trading Platform
✅ Real-time Market Data  
✅ Professional Charts
✅ Portfolio Management
✅ Social Trading Features
✅ Secure & Optimized

SYSTEM REQUIREMENTS:
===================
- Windows 7/8/10/11 (64-bit)
- 4GB RAM minimum
- Internet connection required

SUPPORT:
========
Website: https://www.tradingview.com
Support: https://www.tradingview.com/support

Version: 4.2.1
Build: Professional Edition
"""
            
            zf.writestr("README.txt", readme_content)
            print(f"   ✅ Added: README.txt")
        
        return zip_file

    def create_self_extracting_exe(self):
        """Tạo self-extracting executable bằng Python"""
        print("📦 Creating self-extracting installer...")
        
        # Tạo script tự giải nén
        extractor_script = '''#!/usr/bin/env python3
import os
import sys
import zipfile
import tempfile
import subprocess
from pathlib import Path

# Embedded ZIP data sẽ được thêm vào cuối file này
ZIP_DATA_MARKER = b"__ZIP_DATA_START__"

def extract_and_run():
    """Extract embedded ZIP and run installer"""
    print("TradingView Desktop Installer")
    print("=" * 40)
    
    # Tìm ZIP data trong file này
    with open(sys.argv[0], "rb") as f:
        content = f.read()
    
    zip_start = content.find(ZIP_DATA_MARKER)
    if zip_start == -1:
        print("Error: ZIP data not found!")
        return False
    
    zip_data = content[zip_start + len(ZIP_DATA_MARKER):]
    
    # Tạo temp directory
    with tempfile.TemporaryDirectory() as temp_dir:
        temp_path = Path(temp_dir)
        
        # Extract ZIP
        zip_file = temp_path / "installer.zip"
        with open(zip_file, "wb") as f:
            f.write(zip_data)
        
        with zipfile.ZipFile(zip_file, 'r') as zf:
            zf.extractall(temp_path)
        
        # Run TradingView
        exe_file = temp_path / "TradingView.exe"
        if exe_file.exists():
            print("Starting TradingView Desktop...")
            subprocess.Popen([str(exe_file)])
            return True
        else:
            print("Error: TradingView.exe not found!")
            return False

if __name__ == "__main__":
    try:
        success = extract_and_run()
        if not success:
            input("Press Enter to exit...")
    except Exception as e:
        print(f"Error: {e}")
        input("Press Enter to exit...")
'''
        
        # Tạo ZIP data
        zip_data = b""
        zip_file = self.output_dir / "temp_installer.zip"
        
        with zipfile.ZipFile(zip_file, 'w', zipfile.ZIP_DEFLATED) as zf:
            # Add executable
            exe_source = Path("dist/TradingView.exe")
            if exe_source.exists():
                zf.write(exe_source, "TradingView.exe")
            
            # Add logo
            if os.path.exists("logo.ico"):
                zf.write("logo.ico", "logo.ico")
        
        # Đọc ZIP data
        with open(zip_file, "rb") as f:
            zip_data = f.read()
        
        # Tạo self-extracting executable
        installer_exe = self.output_dir / "TradingView_Installer.py"
        with open(installer_exe, "w", encoding="utf-8") as f:
            f.write(extractor_script)
        
        # Thêm ZIP data
        with open(installer_exe, "ab") as f:
            f.write(b"__ZIP_DATA_START__")
            f.write(zip_data)
        
        # Cleanup
        zip_file.unlink()
        
        print(f"   ✅ Created: {installer_exe}")
        return installer_exe

    def show_results(self):
        """Hiển thị kết quả"""
        print("\n📦 Installer Results:")
        print("=" * 50)
        
        output_files = list(self.output_dir.iterdir())
        if not output_files:
            print("❌ No installer files created!")
            return
        
        for file_path in output_files:
            if file_path.is_file():
                size_mb = file_path.stat().st_size / (1024 * 1024)
                print(f"📦 {file_path.name}: {size_mb:.1f} MB")
                print(f"   📁 Path: {file_path.absolute()}")
            elif file_path.is_dir():
                print(f"📁 {file_path.name}/ (Portable)")
                print(f"   📁 Path: {file_path.absolute()}")

def main():
    print("📦 Simple Installer Creator")
    print("Tạo installer đơn giản không cần NSIS/Inno Setup")
    print("=" * 60)
    
    # Check if TradingView.exe exists
    if not os.path.exists("dist/TradingView.exe"):
        print("❌ TradingView.exe not found in dist/")
        print("💡 Run build.py first to create TradingView.exe")
        return False
    
    installer = SimpleInstaller()
    
    # Create different installer types
    installer.create_portable_package()
    installer.create_zip_installer()
    installer.create_self_extracting_exe()
    
    # Show results
    installer.show_results()
    
    print("\n🎉 SUCCESS!")
    print("📦 Multiple installer formats created!")
    print("📁 Check installer_output/ folder")
    print("\n💡 Installer options:")
    print("   📁 Portable: Extract and run directly")
    print("   📦 ZIP: Full installation with shortcuts")
    print("   🚀 Self-extracting: Python-based installer")
    
    return True

if __name__ == "__main__":
    try:
        success = main()
        if not success:
            input("Press Enter to exit...")
    except Exception as e:
        print(f"\n❌ Error: {e}")
        input("Press Enter to exit...")
